<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { NCard, NTag, NButton } from 'naive-ui';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import { useEcharts } from '@/hooks/common/echarts';

interface Props {
  performanceData?: Api.Monitor.PerformanceData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  performanceData: null,
  loading: false
});

const router = useRouter();

// 计算性能统计
const performanceStats = computed(() => {
  if (!props.performanceData) {
    return {
      avgResponseTime: 0,
      requestCount: 0,
      errorRate: 0,
      requestsPerMinute: 0
    };
  }

  const api = props.performanceData.api_performance;
  const rawErrorRate = api.error_rate || 0;
  const errorRate = rawErrorRate <= 1 ? rawErrorRate * 100 : rawErrorRate;

  return {
    avgResponseTime: api.avg_response_time * 1000, // 转换为毫秒
    requestCount: api.request_count,
    errorRate: Math.min(errorRate, 100),
    requestsPerMinute: api.requests_per_minute
  };
});

// 响应时间状态
const responseTimeStatus = computed(() => {
  const avgTime = performanceStats.value.avgResponseTime;
  if (avgTime < 1000) {
    return { type: 'success' as const, text: '优秀', color: '#10b981' };
  } else if (avgTime < 3000) {
    return { type: 'warning' as const, text: '良好', color: '#f59e0b' };
  } else {
    return { type: 'error' as const, text: '较慢', color: '#ef4444' };
  }
});

// 错误率状态
const errorRateStatus = computed(() => {
  const rate = performanceStats.value.errorRate;
  if (rate < 1) {
    return { type: 'success' as const, text: '优秀', color: '#10b981' };
  } else if (rate < 5) {
    return { type: 'warning' as const, text: '良好', color: '#f59e0b' };
  } else {
    return { type: 'error' as const, text: '较高', color: '#ef4444' };
  }
});

// 响应时间圆形进度图
const { domRef: responseTimeChartRef, updateOptions: updateResponseTimeChart } = useEcharts(() => ({
  series: [{
    type: 'pie',
    radius: ['60%', '80%'],
    center: ['50%', '50%'],
    startAngle: 90,
    clockwise: false,
    avoidLabelOverlap: false,
    hoverAnimation: false,
    label: { show: false },
    labelLine: { show: false },
    data: [
      {
        value: 0,
        name: '响应时间',
        itemStyle: {
          color: '#10b981',
          borderRadius: 8
        }
      },
      {
        value: 100,
        name: '剩余',
        itemStyle: {
          color: 'rgba(0, 0, 0, 0.05)',
          borderWidth: 0
        },
        emphasis: { disabled: true }
      }
    ]
  }],
  graphic: [{
    type: 'text',
    left: 'center',
    top: 'center',
    style: {
      text: '0ms',
      fontSize: 20,
      fontWeight: 'bold',
      fill: '#1f2937'
    }
  }]
}));

// 监听数据变化，更新图表
watch(() => props.performanceData, () => {
  if (props.performanceData) {
    const stats = performanceStats.value;
    const status = responseTimeStatus.value;

    // 计算响应时间百分比（基于5秒最大值）
    const maxTime = 5000;
    const percentage = Math.min((stats.avgResponseTime / maxTime) * 100, 100);

    // 更新圆形进度图
    updateResponseTimeChart(prev => ({
      ...prev,
      series: [{
        ...prev.series[0],
        data: [
          {
            value: percentage,
            name: '响应时间',
            itemStyle: {
              color: status.color,
              borderRadius: 8
            }
          },
          {
            value: 100 - percentage,
            name: '剩余',
            itemStyle: {
              color: 'rgba(0, 0, 0, 0.05)',
              borderWidth: 0
            },
            emphasis: { disabled: true }
          }
        ]
      }],
      graphic: [{
        type: 'text',
        left: 'center',
        top: 'center',
        style: {
          text: `${Math.round(stats.avgResponseTime)}ms`,
          fontSize: 20,
          fontWeight: 'bold',
          fill: status.color
        }
      }]
    }));
  }
}, { immediate: true });

function goToPerformanceDetail() {
  router.push('/monitor/performance');
}
</script>

<template>
  <NCard class="modern-performance-card">
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <div class="header-icon">
            <Icon icon="mdi:speedometer" />
          </div>
          <div class="header-content">
            <h3 class="header-title">性能监控</h3>
            <div class="header-status">
              <div class="status-dot" :style="{ backgroundColor: responseTimeStatus.color }"></div>
              <span class="status-text">{{ responseTimeStatus.text }}</span>
            </div>
          </div>
        </div>
        <NButton text @click="goToPerformanceDetail" class="detail-btn">
          <template #icon>
            <Icon icon="mdi:arrow-right" />
          </template>
          详情
        </NButton>
      </div>
    </template>

    <div class="card-content">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 响应时间圆形图 -->
        <div class="chart-section">
          <div ref="responseTimeChartRef" class="response-chart"></div>
          <div class="chart-label">响应时间</div>
        </div>

        <!-- 指标网格 -->
        <div class="metrics-grid">
          <!-- 总请求数 -->
          <div class="metric-item">
            <div class="metric-icon">
              <Icon icon="mdi:counter" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.requestCount.toLocaleString() }}</div>
              <div class="metric-label">总请求数</div>
            </div>
          </div>

          <!-- 错误率 -->
          <div class="metric-item">
            <div class="metric-icon" :style="{ color: errorRateStatus.color }">
              <Icon icon="mdi:alert-circle-outline" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.errorRate.toFixed(1) }}%</div>
              <div class="metric-label">错误率</div>
              <NTag :type="errorRateStatus.type" size="small" class="metric-tag">
                {{ errorRateStatus.text }}
              </NTag>
            </div>
          </div>

          <!-- 每分钟请求数 -->
          <div class="metric-item">
            <div class="metric-icon">
              <Icon icon="mdi:clock-fast" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.requestsPerMinute.toFixed(0) }}</div>
              <div class="metric-label">请求/分钟</div>
            </div>
          </div>

          <!-- 平均响应时间 -->
          <div class="metric-item">
            <div class="metric-icon" :style="{ color: responseTimeStatus.color }">
              <Icon icon="mdi:timer-outline" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.avgResponseTime.toFixed(0) }}ms</div>
              <div class="metric-label">平均响应</div>
              <NTag :type="responseTimeStatus.type" size="small" class="metric-tag">
                {{ responseTimeStatus.text }}
              </NTag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </NCard>
</template>

<style scoped>
.modern-performance-card {
  height: 100%;
  transition: all 0.3s ease;
}

.modern-performance-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  position: relative;
  overflow: hidden;
}

.header-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  transform: scale(0);
  transition: transform 0.3s ease;
}

.modern-performance-card:hover .header-icon::before {
  transform: scale(1);
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.header-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-text {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.detail-btn {
  font-size: 12px;
  color: #6b7280;
}

.card-content {
  padding: 0;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  align-items: center;
}

.chart-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.response-chart {
  width: 120px;
  height: 120px;
}

.chart-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.metric-item:hover {
  transform: translateY(-1px);
  background: rgba(255, 255, 255, 0.98);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-icon {
  font-size: 16px;
  color: #6b7280;
  flex-shrink: 0;
}

.metric-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
  min-width: 0;
}

.metric-value {
  font-size: 14px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  transition: all 0.3s ease;
}

.metric-value:hover {
  transform: scale(1.05);
}

.metric-label {
  font-size: 10px;
  color: #6b7280;
  font-weight: 500;
}

.metric-tag {
  margin-top: 2px;
  font-size: 9px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes dataUpdate {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.modern-performance-card.data-updated {
  animation: dataUpdate 0.6s ease-out;
}

.modern-performance-card.data-updated .metric-value {
  animation: pulse 0.8s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .response-chart {
    width: 100px;
    height: 100px;
  }
}
</style>
