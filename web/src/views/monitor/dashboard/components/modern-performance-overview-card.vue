<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { NCard, NTag, NButton } from 'naive-ui';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import { useEcharts } from '@/hooks/common/echarts';

interface Props {
  performanceData?: Api.Monitor.PerformanceData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  performanceData: null,
  loading: false
});

const router = useRouter();

// 计算性能统计
const performanceStats = computed(() => {
  if (!props.performanceData) {
    return {
      avgResponseTime: 0,
      requestCount: 0,
      errorRate: 0,
      requestsPerMinute: 0
    };
  }

  const api = props.performanceData.api_performance;
  const rawErrorRate = api.error_rate || 0;
  const errorRate = rawErrorRate <= 1 ? rawErrorRate * 100 : rawErrorRate;

  return {
    avgResponseTime: api.avg_response_time * 1000, // 转换为毫秒
    requestCount: api.request_count,
    errorRate: Math.min(errorRate, 100),
    requestsPerMinute: api.requests_per_minute
  };
});

// 响应时间状态
const responseTimeStatus = computed(() => {
  const avgTime = performanceStats.value.avgResponseTime;
  if (avgTime < 1000) {
    return { type: 'success' as const, text: '优秀', color: '#10b981' };
  } else if (avgTime < 3000) {
    return { type: 'warning' as const, text: '良好', color: '#f59e0b' };
  } else {
    return { type: 'error' as const, text: '较慢', color: '#ef4444' };
  }
});

// 错误率状态
const errorRateStatus = computed(() => {
  const rate = performanceStats.value.errorRate;
  if (rate < 1) {
    return { type: 'success' as const, text: '优秀', color: '#10b981' };
  } else if (rate < 5) {
    return { type: 'warning' as const, text: '良好', color: '#f59e0b' };
  } else {
    return { type: 'error' as const, text: '较高', color: '#ef4444' };
  }
});

// 响应时间圆形进度图
const { domRef: responseTimeChartRef, updateOptions: updateResponseTimeChart } = useEcharts(() => ({
  series: [{
    type: 'pie',
    radius: ['60%', '80%'],
    center: ['50%', '50%'],
    startAngle: 90,
    clockwise: false,
    avoidLabelOverlap: false,
    hoverAnimation: false,
    label: { show: false },
    labelLine: { show: false },
    data: [
      {
        value: 0,
        name: '响应时间',
        itemStyle: {
          color: '#10b981',
          borderRadius: 8
        }
      },
      {
        value: 100,
        name: '剩余',
        itemStyle: {
          color: 'rgba(0, 0, 0, 0.05)',
          borderWidth: 0
        },
        emphasis: { disabled: true }
      }
    ]
  }],
  graphic: [{
    type: 'text',
    left: 'center',
    top: 'center',
    style: {
      text: '0ms',
      fontSize: 20,
      fontWeight: 'bold',
      fill: '#1f2937'
    }
  }]
}));

// 监听数据变化，更新图表
watch(() => props.performanceData, () => {
  if (props.performanceData) {
    const stats = performanceStats.value;
    const status = responseTimeStatus.value;

    // 计算响应时间百分比（基于5秒最大值）
    const maxTime = 5000;
    const percentage = Math.min((stats.avgResponseTime / maxTime) * 100, 100);

    // 更新圆形进度图
    updateResponseTimeChart(prev => ({
      ...prev,
      series: [{
        ...prev.series[0],
        data: [
          {
            value: percentage,
            name: '响应时间',
            itemStyle: {
              color: status.color,
              borderRadius: 8
            }
          },
          {
            value: 100 - percentage,
            name: '剩余',
            itemStyle: {
              color: 'rgba(0, 0, 0, 0.05)',
              borderWidth: 0
            },
            emphasis: { disabled: true }
          }
        ]
      }],
      graphic: [{
        type: 'text',
        left: 'center',
        top: 'center',
        style: {
          text: `${Math.round(stats.avgResponseTime)}ms`,
          fontSize: 20,
          fontWeight: 'bold',
          fill: status.color
        }
      }]
    }));
  }
}, { immediate: true });

function goToPerformanceDetail() {
  router.push('/monitor/performance');
}
</script>

<template>
  <NCard class="modern-performance-card">


    <template #header>
      <div class="card-header">
        <div class="header-left">
          <div class="header-icon">
            <Icon icon="mdi:speedometer" />
            <div class="icon-glow"></div>
          </div>
          <div class="header-content">
            <h3 class="header-title">
              <Icon icon="mdi:chart-line" class="title-icon" />
              性能监控
            </h3>
            <div class="header-status">
              <div class="status-indicator">
                <div class="status-dot" :style="{ backgroundColor: responseTimeStatus.color }"></div>
                <span class="status-text">{{ responseTimeStatus.text }}</span>
                <div class="status-wave" :style="{ backgroundColor: responseTimeStatus.color }"></div>
              </div>
            </div>
          </div>
        </div>
        <NButton text @click="goToPerformanceDetail" class="detail-btn">
          <template #icon>
            <Icon icon="mdi:arrow-top-right" />
          </template>
          详情
        </NButton>
      </div>
    </template>

    <div class="card-content">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 响应时间圆形图 -->
        <div class="chart-section">
          <div class="chart-container">
            <div ref="responseTimeChartRef" class="response-chart"></div>
            <div class="chart-overlay">
              <div class="chart-center-text">
                <div class="center-value">{{ performanceStats.avgResponseTime.toFixed(0) }}</div>
                <div class="center-unit">ms</div>
              </div>
            </div>
          </div>
          <div class="chart-label">
            <Icon icon="mdi:timer" class="label-icon" />
            响应时间
          </div>
        </div>

        <!-- 指标网格 -->
        <div class="metrics-grid">
          <!-- 总请求数 -->
          <div class="metric-item requests">
            <div class="metric-background"></div>
            <div class="metric-icon-wrapper">
              <Icon icon="mdi:counter" class="metric-icon" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.requestCount.toLocaleString() }}</div>
              <div class="metric-label">总请求数</div>
            </div>
            <div class="metric-trend">
              <Icon icon="mdi:trending-up" />
            </div>
          </div>

          <!-- 错误率 -->
          <div class="metric-item errors" :class="errorRateStatus.type">
            <div class="metric-background"></div>
            <div class="metric-icon-wrapper">
              <Icon icon="mdi:alert-circle" class="metric-icon" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.errorRate.toFixed(1) }}%</div>
              <div class="metric-label">错误率</div>
              <NTag :type="errorRateStatus.type" size="small" class="metric-tag">
                {{ errorRateStatus.text }}
              </NTag>
            </div>
          </div>

          <!-- 每分钟请求数 -->
          <div class="metric-item rpm">
            <div class="metric-background"></div>
            <div class="metric-icon-wrapper">
              <Icon icon="mdi:clock-fast" class="metric-icon" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.requestsPerMinute.toFixed(0) }}</div>
              <div class="metric-label">请求/分钟</div>
            </div>
            <div class="metric-pulse"></div>
          </div>

          <!-- 平均响应时间 -->
          <div class="metric-item response-time" :class="responseTimeStatus.type">
            <div class="metric-background"></div>
            <div class="metric-icon-wrapper">
              <Icon icon="mdi:timer-outline" class="metric-icon" />
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.avgResponseTime.toFixed(0) }}ms</div>
              <div class="metric-label">平均响应</div>
              <NTag :type="responseTimeStatus.type" size="small" class="metric-tag">
                {{ responseTimeStatus.text }}
              </NTag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </NCard>
</template>

<style scoped>
.modern-performance-card {
  height: 100%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  background: white;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.modern-performance-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  position: relative;
  z-index: 2;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.header-icon:hover {
  transform: rotate(10deg) scale(1.1);
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
}

.icon-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 12px;
  animation: iconGlow 3s ease-in-out infinite;
}

@keyframes iconGlow {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.1); }
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-title {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 20px;
  color: #3b82f6;
  animation: titleIconSpin 4s linear infinite;
}

@keyframes titleIconSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.header-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  background: rgba(248, 250, 252, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.6);
  position: relative;
  overflow: hidden;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  animation: statusPulse 2s infinite;
  box-shadow: 0 0 10px currentColor;
}

.status-wave {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  opacity: 0.3;
  animation: statusWave 2s infinite;
}

@keyframes statusPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes statusWave {
  0% { transform: scale(1); opacity: 0.3; }
  100% { transform: scale(3); opacity: 0; }
}

.status-text {
  font-size: 12px;
  color: #374151;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-btn {
  font-size: 12px;
  color: #3b82f6;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(248, 250, 252, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s ease;
}

.detail-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateX(4px);
}

.card-content {
  padding: 20px;
  position: relative;
  z-index: 2;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  align-items: center;
}

.chart-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.chart-container {
  position: relative;
  width: 140px;
  height: 140px;
}

.response-chart {
  width: 100%;
  height: 100%;
}

.chart-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.chart-center-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.center-value {
  font-size: 24px;
  font-weight: 800;
  color: #1f2937;
  animation: valueGlow 3s ease-in-out infinite;
}

.center-unit {
  font-size: 12px;
  color: #6b7280;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

@keyframes valueGlow {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.chart-label {
  font-size: 14px;
  color: #374151;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(248, 250, 252, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.label-icon {
  font-size: 16px;
  animation: labelIconBounce 2s ease-in-out infinite;
}

@keyframes labelIconBounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.metric-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.metric-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  transition: opacity 0.3s ease;
}

.metric-item.requests .metric-background {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.metric-item.errors .metric-background {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.metric-item.rpm .metric-background {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.metric-item.response-time .metric-background {
  background: linear-gradient(135deg, #feca57, #ff9ff3);
}

.metric-item:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

.metric-item:hover .metric-background {
  opacity: 0.2;
}

.metric-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.metric-item.requests .metric-icon-wrapper {
  background: linear-gradient(135deg, #667eea, #764ba2);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.metric-item.errors .metric-icon-wrapper {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.metric-item.rpm .metric-icon-wrapper {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
}

.metric-item.response-time .metric-icon-wrapper {
  background: linear-gradient(135deg, #feca57, #ff9ff3);
  box-shadow: 0 4px 15px rgba(254, 202, 87, 0.4);
}

.metric-icon {
  font-size: 20px;
  color: white;
  z-index: 2;
  animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-2px) rotate(5deg); }
}

.metric-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
  flex: 1;
}

.metric-value {
  font-size: 18px;
  font-weight: 800;
  color: #1f2937;
  line-height: 1;
  transition: all 0.3s ease;
}

.metric-value:hover {
  transform: scale(1.1);
}

.metric-label {
  font-size: 11px;
  color: #6b7280;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-tag {
  margin-top: 4px;
  font-size: 10px;
  font-weight: 600;
}

.metric-trend {
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 14px;
  color: #10b981;
  animation: trendBounce 2s ease-in-out infinite;
}

@keyframes trendBounce {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-3px) rotate(10deg); }
}

.metric-pulse {
  position: absolute;
  top: 50%;
  right: 12px;
  width: 8px;
  height: 8px;
  background: #4ecdc4;
  border-radius: 50%;
  animation: metricPulse 2s infinite;
}

@keyframes metricPulse {
  0% { transform: translateY(-50%) scale(1); opacity: 1; }
  100% { transform: translateY(-50%) scale(2); opacity: 0; }
}

/* 数据更新动画 */
.modern-performance-card.data-updated {
  animation: cardDataUpdate 1s ease-out;
}

.modern-performance-card.data-updated .metric-value {
  animation: valueUpdate 1.2s ease-out;
}

.modern-performance-card.data-updated .chart-center-text {
  animation: chartUpdate 1s ease-out;
}

@keyframes cardDataUpdate {
  0% { transform: translateY(-4px) scale(1.02); }
  50% { transform: translateY(-6px) scale(1.03); box-shadow: 0 16px 40px rgba(0, 0, 0, 0.2); }
  100% { transform: translateY(-4px) scale(1.02); }
}

@keyframes valueUpdate {
  0% { transform: scale(1); }
  30% { transform: scale(1.15); color: #3b82f6; }
  100% { transform: scale(1); }
}

@keyframes chartUpdate {
  0% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.1); }
  100% { transform: translate(-50%, -50%) scale(1); }
}

/* 成功状态样式 */
.metric-item.success {
  border-color: rgba(16, 185, 129, 0.3);
}

.metric-item.success .metric-background {
  background: linear-gradient(135deg, #10b981, #059669);
}

/* 警告状态样式 */
.metric-item.warning {
  border-color: rgba(245, 158, 11, 0.3);
}

.metric-item.warning .metric-background {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

/* 错误状态样式 */
.metric-item.error {
  border-color: rgba(239, 68, 68, 0.3);
}

.metric-item.error .metric-background {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .chart-container {
    width: 120px;
    height: 120px;
  }

  .center-value {
    font-size: 20px;
  }

  .metric-item {
    padding: 12px;
  }

  .metric-value {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-left {
    gap: 12px;
  }

  .header-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .metrics-grid {
    gap: 8px;
  }

  .metric-item {
    padding: 10px;
    gap: 8px;
  }

  .metric-icon-wrapper {
    width: 32px;
    height: 32px;
  }

  .metric-icon {
    font-size: 16px;
  }
}
</style>
